# Use the latest 2.1 version of CircleCI pipeline processing engine, see https://circleci.com/docs/2.0/configuration-reference/
version: 2.1

parameters:
  noir_version:
    type: string
    default: "1.0.0-beta.8"
  bb_version:
    type: string
    default: "1.0.0"

defaults: &defaults
  working_directory: ~/repo
  # https://circleci.com/docs/2.0/circleci-images/#language-image-variants
  docker:
    - image: cimg/node:24.3.0
      environment:
        TERM: xterm # Enable colors in term
  resource_class: large

jobs:
  checkout:
    <<: *defaults
    steps:
      - checkout
      - run:
          name: Install Noir
          command: |
            curl -sSL https://raw.githubusercontent.com/noir-lang/noirup/refs/heads/main/install | bash || true
            echo 'export PATH="/home/<USER>/.nargo/bin:$PATH"' >> $BASH_ENV
            source $BASH_ENV
            noirup -v << pipeline.parameters.noir_version >>
            /home/<USER>/.nargo/bin/nargo -V
      - save_cache:
          paths:
            - /home/<USER>/.nargo
          key: nargo-cache-<< pipeline.parameters.noir_version >>

      - run:
          name: Install BB
          command: |
            curl -L https://raw.githubusercontent.com/AztecProtocol/aztec-packages/refs/heads/master/barretenberg/bbup/install | bash || true
            echo 'export PATH="/home/<USER>/.bb:$PATH"' >> $BASH_ENV
            source $BASH_ENV
            bbup -v << pipeline.parameters.bb_version >>
            /home/<USER>/.bb/bb --version
      - save_cache:
          paths:
            - /home/<USER>/.bb
          key: bb-cache-<< pipeline.parameters.bb_version >>

      - restore_cache:
          keys:
            - node-deps-{{ checksum "package-lock.json" }}
            - node-deps-
      - run:
          name: Install npm dependencies
          command: |
            npm ci
      - save_cache:
          paths:
            - node_modules
          key: node-deps-{{ checksum "package-lock.json" }}
      - persist_to_workspace:
          root: ~/repo
          paths: .

  lint:
    <<: *defaults
    steps:
      - restore_cache:
          keys:
            - nargo-cache-<< pipeline.parameters.noir_version >>
      - attach_workspace:
          at: ~/repo
      - run: PATH=/home/<USER>/.nargo/bin:$PATH npm run lint

  noir_tests:
    <<: *defaults
    steps:
      - restore_cache:
          keys:
            - nargo-cache-<< pipeline.parameters.noir_version >>
      - attach_workspace:
          at: ~/repo
      - run:
          name: nargo test
          command: /home/<USER>/.nargo/bin/nargo test

  test_circuits:
    <<: *defaults
    steps:
      - restore_cache:
          keys:
            - nargo-cache-<< pipeline.parameters.noir_version >>
      - restore_cache:
          keys:
            - bb-cache-<< pipeline.parameters.bb_version >>
      - attach_workspace:
          at: ~/repo
      - run:
          name: Compile circuits
          command: |
            PATH=/home/<USER>/.nargo/bin:$PATH ~/repo/scripts/ci-compile-circuits.sh
      - run:
          name: Integration tests
          command: |
            PATH=/home/<USER>/.bb:$PATH npm test circuits.test.ts

workflows:
  version: 2

  test:
    jobs:
      - checkout
      - lint:
          requires:
            - checkout
      - test_circuits:
          requires:
            - lint
