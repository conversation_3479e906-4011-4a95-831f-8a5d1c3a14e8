[package]
name = "sig_check_id_data_tbs_1000_ecdsa_brainpool_256r1_sha384"
type = "bin"
authors = ["<PERSON>", "<PERSON>"]
compiler_version = ">=1.0.0"

[dependencies]
sig_check_ecdsa = { path = "../../../../../../../../lib/sig-check/ecdsa" }
utils = { path = "../../../../../../../../lib/utils" }
data_check_tbs_pubkey = { path = "../../../../../../../../lib/data-check/tbs-pubkey" }
commitment = { path = "../../../../../../../../lib/commitment/dsc-to-id" }
sig_check_common = { path = "../../../../../../../../lib/sig-check/common" }
